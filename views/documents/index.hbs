{{!< ../layout}}

<section class="section">
  <div class="container">
    <div class="level">
      <div class="level-left">
        <h1 class="title">My Documents</h1>
      </div>
      <div class="level-right">
        <a href="/documents/upload" class="button is-primary">
          <span class="icon">
            <i class="fas fa-upload"></i>
          </span>
          <span>Upload Document</span>
        </a>
      </div>
    </div>

    <!-- Storage Quota -->
    <div class="box">
      <h2 class="subtitle">Storage Usage</h2>
      <div class="content">
        <div class="level is-mobile">
          <div class="level-left">
            <div class="level-item" style="gap: 0.25rem">
              <strong>{{usedMB}} MB</strong> of <strong>{{quotaMB}} MB</strong> used
            </div>
          </div>
          <div class="level-right">
            <div class="level-item">
              <strong>{{quotaPercentage}}%</strong> used
            </div>
          </div>
        </div>
        <progress
          class="progress {{#if (gte quotaPercentage 90)}}is-danger{{else if (gte quotaPercentage 75)}}is-warning{{else}}is-primary{{/if}}"
          value="{{quotaPercentage}}"
          max="100">
          {{quotaPercentage}}%
        </progress>
        <p class="help">{{remainingMB}} MB remaining</p>
      </div>
    </div>

    <!-- Documents List -->
    {{#if documents.length}}
      <div class="table-container">
        <table class="table is-fullwidth is-hoverable">
          <thead>
            <tr>
              <th>Name</th>
              <th>Author</th>
              <th>Type</th>
              <th>Size</th>
              <th>Uploaded</th>
              <th class="has-text-centered">Actions</th>
            </tr>
          </thead>
          <tbody>
            {{#each documents}}
              {{> document-table-row this showActions="true"}}
            {{/each}}
          </tbody>
        </table>
      </div>
    {{else}}
      <div class="notification is-light">
        <div class="content has-text-centered">
          <p class="mb-4">
            <span class="icon is-large">
              <i class="fas fa-file-upload fa-3x"></i>
            </span>
          </p>
          <p class="subtitle">No documents found</p>
          <p class="mb-4">Upload your first document to get started!</p>
          <a href="/documents/upload" class="button is-primary">
            <span class="icon">
              <i class="fas fa-upload"></i>
            </span>
            <span>Upload Document</span>
          </a>
        </div>
      </div>
    {{/if}}
  </div>
</section>

<!-- Delete Confirmation Modal -->
<div class="modal" id="deleteModal">
  <div class="modal-background"></div>
  <div class="modal-card">
    <header class="modal-card-head">
      <p class="modal-card-title">Delete Document</p>
      <button class="delete" aria-label="close"></button>
    </header>
    <section class="modal-card-body">
      <p>Are you sure you want to delete this document? This action cannot be undone.</p>
    </section>
    <footer class="modal-card-foot">
      <button class="button is-danger" id="confirmDelete">
        <span class="icon">
          <i class="fas fa-trash"></i>
        </span>
        <span>Delete</span>
      </button>
      <button class="button" id="cancelDelete">
        <span class="icon">
          <i class="fas fa-times"></i>
        </span>
        <span>Cancel</span>
      </button>
    </footer>
  </div>
</div>

<!-- Edit Document Modal -->
<div class="modal" id="editModal">
  <div class="modal-background"></div>
  <div class="modal-card">
    <header class="modal-card-head">
      <p class="modal-card-title">Edit Document</p>
      <button class="delete" aria-label="close"></button>
    </header>
    <section class="modal-card-body">
      <form id="editDocumentForm">
        <div class="field">
          <label class="label">Document Name</label>
          <div class="control">
            <input class="input" type="text" id="editName" name="name" required>
          </div>
        </div>
        <div class="field">
          <label class="label">Author(s)</label>
          <div class="control">
            <input class="input" type="text" id="editAuthors" name="authors" required>
          </div>
        </div>
      </form>
    </section>
    <footer class="modal-card-foot">
      <button class="button is-primary" id="confirmEdit">
        <span class="icon">
          <i class="fas fa-save"></i>
        </span>
        <span>Save Changes</span>
      </button>
      <button class="button" id="cancelEdit">
        <span class="icon">
          <i class="fas fa-times"></i>
        </span>
        <span>Cancel</span>
      </button>
    </footer>
  </div>
</div>

{{#section 'scripts'}}
<script>
  document.addEventListener('DOMContentLoaded', () => {
    // Initialize all dropdowns
    const $dropdowns = document.querySelectorAll('.dropdown:not(.is-hoverable)');
    if ($dropdowns.length > 0) {
      $dropdowns.forEach($el => {
        $el.addEventListener('click', (event) => {
          event.stopPropagation();
          $el.classList.toggle('is-active');
        });
      });

      // Close dropdowns when clicking outside
      document.addEventListener('click', () => {
        $dropdowns.forEach($el => $el.classList.remove('is-active'));
      });
    }

    // Initialize mobile menu
    const $navbarBurgers = Array.prototype.slice.call(document.querySelectorAll('.navbar-burger'), 0);
    if ($navbarBurgers.length > 0) {
      $navbarBurgers.forEach(el => {
        el.addEventListener('click', () => {
          const target = el.dataset.target;
          const $target = document.getElementById(target);
          el.classList.toggle('is-active');
          $target.classList.toggle('is-active');
        });
      });
    }

    // Close notifications
    (document.querySelectorAll('.notification .delete') || []).forEach(($delete) => {
      $delete.addEventListener('click', () => {
        $delete.parentNode.remove();
      });
    });

    // Edit document functionality
    const editModal = document.getElementById('editModal');
    const editForm = document.getElementById('editDocumentForm');
    const editNameInput = document.getElementById('editName');
    const editAuthorsInput = document.getElementById('editAuthors');
    let documentIdToEdit = null;

    // Set up edit buttons
    document.querySelectorAll('.edit-document').forEach(button => {
      button.addEventListener('click', (e) => {
        e.preventDefault();
        documentIdToEdit = button.dataset.id;
        editNameInput.value = button.dataset.name;
        editAuthorsInput.value = button.dataset.authors;
        editModal.classList.add('is-active');
      });
    });

    // Confirm edit
    document.getElementById('confirmEdit').addEventListener('click', async () => {
      if (!documentIdToEdit) return;

      try {
        const response = await fetch(`/documents/${documentIdToEdit}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
          },
          body: JSON.stringify({
            name: editNameInput.value,
            authors: editAuthorsInput.value
          })
        });

        if (response.ok) {
          window.location.reload();
        } else {
          const error = await response.json();
          showNotification(error.message || 'Failed to update document', 'is-danger');
        }
      } catch (error) {
        console.error('Error updating document:', error);
        showNotification('An error occurred while updating the document', 'is-danger');
      } finally {
        editModal.classList.remove('is-active');
      }
    });

    // Close edit modal
    document.querySelectorAll('#editModal .modal-background, #editModal .delete, #cancelEdit').forEach(el => {
      el.addEventListener('click', () => {
        editModal.classList.remove('is-active');
      });
    });

    // Delete document functionality
    const deleteModal = document.getElementById('deleteModal');
    let documentIdToDelete = null;

    // Set up delete buttons
    document.querySelectorAll('.delete-document').forEach(button => {
      button.addEventListener('click', (e) => {
        e.preventDefault();
        documentIdToDelete = button.dataset.id;
        deleteModal.classList.add('is-active');
      });
    });

    // Confirm delete
    document.getElementById('confirmDelete').addEventListener('click', async () => {
      if (!documentIdToDelete) return;

      try {
        const response = await fetch(`/documents/${documentIdToDelete}`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
          }
        });

        if (response.ok) {
          window.location.reload();
        } else {
          const error = await response.json();
          showNotification(error.message || 'Failed to delete document', 'is-danger');
        }
      } catch (error) {
        console.error('Error deleting document:', error);
        showNotification('An error occurred while deleting the document', 'is-danger');
      } finally {
        deleteModal.classList.remove('is-active');
      }
    });

    // Close modal
    document.querySelectorAll('.modal-background, .delete, #cancelDelete').forEach(el => {
      el.addEventListener('click', () => {
        deleteModal.classList.remove('is-active');
      });
    });

    // Helper function to show notifications
    function showNotification(message, type = 'is-success') {
      const notification = document.createElement('div');
      notification.className = `notification ${type} is-light is-radiusless mb-0`;
      notification.innerHTML = `
        <button class="delete"></button>
        <p>${message}</p>
      `;

      // Insert after the navbar
      const navbar = document.querySelector('.navbar');
      navbar.parentNode.insertBefore(notification, navbar.nextSibling);

      // Auto-remove after 5 seconds
      setTimeout(() => {
        notification.style.opacity = '0';
        setTimeout(() => notification.remove(), 300);
      }, 5000);

      // Close button
      notification.querySelector('.delete').addEventListener('click', () => {
        notification.remove();
      });
    }

    // Make showNotification available globally for other scripts
    window.showNotification = showNotification;
  });
</script>
{{/section}}
