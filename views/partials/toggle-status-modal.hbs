{{!--
  Toggle Status Modal Partial
  
  Reusable modal for confirming user status changes
--}}
<!-- Toggle Status Modal -->
<div class="modal" id="toggleStatusModal">
  <div class="modal-background"></div>
  <div class="modal-card">
    <header class="modal-card-head">
      <p class="modal-card-title">Confirm Status Change</p>
      <button class="delete" aria-label="close"></button>
    </header>
    <section class="modal-card-body">
      <p id="statusMessage"></p>
    </section>
    <footer class="modal-card-foot">
      <form id="toggleStatusForm" method="POST" action="">
        <input type="hidden" name="_method" value="PATCH">
        <input type="hidden" name="is_active" value="">
        <button type="button" class="button is-light cancel-toggle">Cancel</button>
        <button type="submit" class="button is-warning">Confirm</button>
      </form>
    </footer>
  </div>
</div>
