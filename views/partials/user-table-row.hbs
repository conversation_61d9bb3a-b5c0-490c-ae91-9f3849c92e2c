{{!--
  User Table Row Partial
  
  Receives the user object directly via {{> user-table-row this}}
--}}
<tr>
  <!-- Name Column -->
  <td>
    <div class="media">
      <div class="media-left">
        <figure class="image is-32x32">
          <img 
            src="https://ui-avatars.com/api/?name={{name}}&background=random" 
            alt="{{name}}"
            class="is-rounded"
          >
        </figure>
      </div>
      <div class="media-content">
        <p class="has-text-weight-semibold">{{name}}</p>
        <p class="is-size-7 has-text-grey">ID: {{id}}</p>
      </div>
    </div>
  </td>

  <!-- Email Column -->
  <td>{{email}}</td>

  <!-- Role Column -->
  <td>
    <span class="tag {{#if (eq role 'admin')}}is-danger{{else}}is-info{{/if}}">
      {{role}}
    </span>
  </td>

  <!-- Storage Used Column -->
  <td>
    <div class="content">
      <p>{{getFormattedSize storage_used}} of {{getFormattedSize storage_quota}}</p>
      <progress 
        class="progress is-small {{#if (gt (getStoragePercentage storage_used storage_quota) 90)}}is-danger{{else if (gt (getStoragePercentage storage_used storage_quota) 70)}}is-warning{{else}}is-primary{{/if}}" 
        value="{{storage_used}}" 
        max="{{storage_quota}}"
      >
        {{getStoragePercentage storage_used storage_quota}}%
      </progress>
      <p class="help">{{getStoragePercentage storage_used storage_quota}}% used</p>
    </div>
  </td>

  <!-- Status Column -->
  <td>
    <span class="tag {{#if is_active}}is-success{{else}}is-light{{/if}}">
      {{#if is_active}}Active{{else}}Inactive{{/if}}
    </span>
  </td>

  <!-- Actions Column -->
  <td>
    <div class="buttons are-small">
      <a href="/admin/users/{{id}}/edit" class="button is-info is-light">
        <span class="icon">
          <i class="fas fa-edit"></i>
        </span>
        <span>Edit</span>
      </a>
      {{#unless (eq id ../currentUser.id)}}
        {{#if is_active}}
          <button class="button is-warning is-light toggle-status" data-id="{{id}}" data-status="false" data-name="{{name}}">
            <span class="icon">
              <i class="fas fa-user-slash"></i>
            </span>
            <span>Deactivate</span>
          </button>
        {{else}}
          <button class="button is-success is-light toggle-status" data-id="{{id}}" data-status="true" data-name="{{name}}">
            <span class="icon">
              <i class="fas fa-user-check"></i>
            </span>
            <span>Activate</span>
          </button>
        {{/if}}
        <button class="button is-danger is-light delete-user" data-id="{{id}}" data-name="{{name}}">
          <span class="icon">
            <i class="fas fa-trash"></i>
          </span>
          <span>Delete</span>
        </button>
      {{/unless}}
    </div>
  </td>
</tr>
