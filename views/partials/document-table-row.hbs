{{!--
  Document Table Row Partial

  Receives the document object directly via {{> document-table-row this}}
--}}
<tr>
  <!-- Document Name Column -->
  <td>
    <span class="icon-text">
      <span class="icon">
        {{#if (eq mime_type 'application/pdf')}}
          <i class="fas fa-file-pdf has-text-danger"></i>
        {{else if (or (eq mime_type 'application/msword') (eq mime_type 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'))}}
          <i class="fas fa-file-word has-text-info"></i>
        {{else if (or (eq mime_type 'application/vnd.ms-excel') (eq mime_type 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'))}}
          <i class="fas fa-file-excel has-text-success"></i>
        {{else if (eq mime_type 'text/plain')}}
          <i class="fas fa-file-alt"></i>
        {{else if (eq (split mime_type '/')[0] 'image')}}
          <i class="fas fa-file-image"></i>
        {{else}}
          <i class="fas fa-file"></i>
        {{/if}}
      </span>
      <span class="truncate-text" title="{{name}}">{{name}}</span>
    </span>
  </td>

  <!-- Author/Uploaded By Column -->
  {{#if authors}}
    <td>{{authors}}</td>
  {{else if User}}
    <td>
      {{#if User.name}}
        <a href="/admin/users/{{User.id}}">{{User.name}}</a>
        <span class="is-size-7 has-text-grey is-block">{{User.email}}</span>
      {{else}}
        {{User.email}}
      {{/if}}
    </td>
  {{else}}
    <td>-</td>
  {{/if}}

  <!-- Type Column -->
  {{#if User}}
    <td>
      <span class="tag is-light">{{mime_type}}</span>
    </td>
  {{else}}
    <td>{{mime_type}}</td>
  {{/if}}

  <!-- Size Column -->
  {{#if size_bytes}}
    <td>{{formatFileSize size_bytes}}</td>
  {{else}}
    <td>{{getFormattedSize this}}</td>
  {{/if}}

  <!-- Date Column -->
  {{#if createdAt}}
    <td>{{formatDate createdAt 'YYYY-MM-DD'}}</td>
  {{else}}
    <td>{{formatDate created_at}}</td>
  {{/if}}

  <!-- Actions Column (only show if showActions is true) -->
    <td>
      {{#if authors}}
        {{!-- User context: show user-specific actions --}}
        <div class="buttons is-justify-content-center">
          <a href="/documents/{{id}}/view" class="button is-small is-info" title="View" target="_blank">
            <span class="icon">
              <i class="fas fa-eye"></i>
            </span>
          </a>
          <button class="button is-small is-warning edit-document" data-id="{{id}}" data-name="{{name}}" data-authors="{{authors}}" title="Edit">
            <span class="icon">
              <i class="fas fa-edit"></i>
            </span>
          </button>
          <a href="/documents/{{id}}/download" class="button is-small is-success" title="Download">
            <span class="icon">
              <i class="fas fa-download"></i>
            </span>
          </a>
          <button class="button is-small is-danger delete-document" data-id="{{id}}" title="Delete">
            <span class="icon">
              <i class="fas fa-trash"></i>
            </span>
          </button>
        </div>
      {{else}}
        {{!-- Admin context: show admin-specific actions --}}
        <div class="buttons are-small">
          <a href="/documents/{{id}}/download" class="button is-info is-light" title="Download">
            <span class="icon">
              <i class="fas fa-download"></i>
            </span>
          </a>
          <a href="/documents/{{id}}" class="button is-link is-light" title="View Details">
            <span class="icon">
              <i class="fas fa-eye"></i>
            </span>
          </a>
          <button class="button is-danger is-light delete-document" data-id="{{id}}" data-name="{{name}}" title="Delete">
            <span class="icon">
              <i class="fas fa-trash"></i>
            </span>
          </button>
        </div>
      {{/if}}
    </td>
</tr>
