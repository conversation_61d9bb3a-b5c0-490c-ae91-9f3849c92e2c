{{!--
  User Modals JavaScript Partial
  
  Shared JavaScript functionality for user delete and status toggle modals
--}}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Delete user modal
    const deleteButtons = document.querySelectorAll('.delete-user');
    const deleteModal = document.getElementById('deleteUserModal');
    const deleteForm = document.getElementById('deleteUserForm');
    const deleteUserName = document.getElementById('deleteUserName');
    const cancelDelete = document.querySelector('.cancel-delete');
    
    deleteButtons.forEach(button => {
      button.addEventListener('click', function() {
        const userId = this.getAttribute('data-id');
        const userName = this.getAttribute('data-name') || 'this user';
        deleteUserName.textContent = userName;
        deleteForm.action = `/admin/users/${userId}`;
        deleteModal.classList.add('is-active');
      });
    });
    
    // Close delete modal
    deleteModal.querySelector('.modal-background').addEventListener('click', () => {
      deleteModal.classList.remove('is-active');
    });
    
    deleteModal.querySelector('.delete').addEventListener('click', () => {
      deleteModal.classList.remove('is-active');
    });
    
    cancelDelete.addEventListener('click', () => {
      deleteModal.classList.remove('is-active');
    });
    
    // Toggle status modal
    const toggleButtons = document.querySelectorAll('.toggle-status');
    const toggleModal = document.getElementById('toggleStatusModal');
    const toggleForm = document.getElementById('toggleStatusForm');
    const statusMessage = document.getElementById('statusMessage');
    const cancelToggle = document.querySelector('.cancel-toggle');
    
    toggleButtons.forEach(button => {
      button.addEventListener('click', function() {
        const userId = this.getAttribute('data-id');
        const newStatus = this.getAttribute('data-status') === 'true';
        const userName = this.getAttribute('data-name') || 'this user';
        
        toggleForm.action = `/admin/users/${userId}/status`;
        toggleForm.querySelector('input[name="is_active"]').value = newStatus;
        
        if (newStatus) {
          statusMessage.textContent = `Are you sure you want to activate the user "${userName}"? This will allow them to log in again.`;
          toggleForm.querySelector('button[type="submit"]').className = 'button is-success';
          toggleForm.querySelector('button[type="submit"]').textContent = 'Activate User';
        } else {
          statusMessage.textContent = `Are you sure you want to deactivate the user "${userName}"? They will not be able to log in until reactivated.`;
          toggleForm.querySelector('button[type="submit"]').className = 'button is-warning';
          toggleForm.querySelector('button[type="submit"]').textContent = 'Deactivate User';
        }
        
        toggleModal.classList.add('is-active');
      });
    });
    
    // Close toggle status modal
    toggleModal.querySelector('.modal-background').addEventListener('click', () => {
      toggleModal.classList.remove('is-active');
    });
    
    toggleModal.querySelector('.delete').addEventListener('click', () => {
      toggleModal.classList.remove('is-active');
    });
    
    cancelToggle.addEventListener('click', () => {
      toggleModal.classList.remove('is-active');
    });

    // Close notifications when clicking the delete button
    document.querySelectorAll('.notification .delete').forEach(deleteButton => {
      deleteButton.addEventListener('click', function() {
        this.closest('.notification').remove();
      });
    });
  });
</script>
