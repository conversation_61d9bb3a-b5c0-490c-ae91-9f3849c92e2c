{{!< ../../layout}}

{{#content 'head'}}
  <style>
    .admin-dashboard .card {
      height: 100%;
      transition: transform 0.2s, box-shadow 0.2s;
    }
    .admin-dashboard .card:hover {
      transform: translateY(-5px);
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    .admin-dashboard .icon.is-large {
      height: 3rem;
      width: 3rem;
    }
    .admin-dashboard .level-item {
      gap: 0.75rem;
    }
  </style>
{{/content}}

<section class="section">
  <div class="container admin-dashboard">
    <div class="level mb-5">
  <div class="level-left">
    <h1 class="title">Admin Dashboard</h1>
  </div>
  <div class="level-right">
    <div class="buttons">
      <a href="/admin/users/new" class="button is-primary">
        <span class="icon">
          <i class="fas fa-user-plus"></i>
        </span>
        <span>Add User</span>
      </a>
    </div>
  </div>
</div>

<!-- Stats Cards -->
<div class="columns is-multiline">
  <!-- Total Users -->
  <div class="column is-12-mobile is-6-tablet is-3-desktop">
    <div class="card">
      <div class="card-content">
        <div class="level is-mobile">
          <div class="level-left">
            <div class="level-item">
              <div class="icon is-large has-text-info">
                <i class="fas fa-users fa-2x"></i>
              </div>
            </div>
            <div class="level-item">
              <div>
                <p class="heading">Total Users</p>
                <p class="title">{{totalUsers}}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <footer class="card-footer">
        <a href="/admin/users" class="card-footer-item">View All</a>
      </footer>
    </div>
  </div>

  <!-- Total Documents -->
  <div class="column is-12-mobile is-6-tablet is-3-desktop">
    <div class="card">
      <div class="card-content">
        <div class="level is-mobile">
          <div class="level-left">
            <div class="level-item">
              <div class="icon is-large has-text-success">
                <i class="fas fa-file-alt fa-2x"></i>
              </div>
            </div>
            <div class="level-item">
              <div>
                <p class="heading">Total Documents</p>
                <p class="title">{{totalDocuments}}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <footer class="card-footer">
        <a href="/admin/documents" class="card-footer-item">View All</a>
      </footer>
    </div>
  </div>

  <!-- Total Storage Used -->
  <div class="column is-12-mobile is-6-tablet is-3-desktop">
    <div class="card">
      <div class="card-content">
        <div class="level is-mobile">
          <div class="level-left">
            <div class="level-item">
              <div class="icon is-large has-text-warning">
                <i class="fas fa-hdd fa-2x"></i>
              </div>
            </div>
            <div class="level-item">
              <div>
                <p class="heading">Storage Used</p>
                <p class="title">{{totalStorageMB}} MB</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <footer class="card-footer">
        <div class="card-footer-item">
          <div class="content">
            <progress class="progress is-small is-warning" value="{{totalStorageMB}}" max="10000">{{totalStorageMB}} MB</progress>
            <p class="help">Out of 10,000 MB</p>
          </div>
        </div>
      </footer>
    </div>
  </div>

  <!-- Recent Activity -->
  <div class="column is-12-mobile is-6-tablet is-3-desktop">
    <div class="card">
      <div class="card-content">
        <div class="level is-mobile">
          <div class="level-left">
            <div class="level-item">
              <div class="icon is-large has-text-primary">
                <i class="fas fa-clock fa-2x"></i>
              </div>
            </div>
            <div class="level-item">
              <div>
                <p class="heading">Recent Activity</p>
                <p class="title">Latest</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <footer class="card-footer">
        <a href="#" class="card-footer-item">View Logs</a>
      </footer>
    </div>
  </div>
</div>

<!-- Recent Documents -->
<div class="box">
  <div class="level">
    <div class="level-left">
      <h2 class="subtitle">Recently Uploaded Documents</h2>
    </div>
    <div class="level-right">
      <a href="/admin/documents" class="button is-small">View All</a>
    </div>
  </div>

  {{#if recentDocuments.length}}
    <div class="table-container">
      <table class="table is-fullwidth is-hoverable">
        <thead>
          <tr>
            <th>Name</th>
            <th>Uploaded By</th>
            <th>Type</th>
            <th>Size</th>
            <th>Uploaded</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {{#each recentDocuments}}
            {{> document-table-row this }}
          {{/each}}
        </tbody>
      </table>
    </div>
  {{else}}
    <div class="has-text-centered py-5">
      <span class="icon is-large has-text-grey-light">
        <i class="fas fa-file-upload fa-3x"></i>
      </span>
      <p class="has-text-grey">No documents have been uploaded yet</p>
    </div>
  {{/if}}
</div>

<!-- System Status -->
<div class="columns">
  <div class="column">
    <div class="box">
      <h2 class="subtitle">System Status</h2>
      <div class="content">
        <div class="level">
          <div class="level-item has-text-centered">
            <div>
              <p class="heading">Database</p>
              <p class="title has-text-success">
                <span class="icon">
                  <i class="fas fa-check-circle"></i>
                </span>
                <span>Online</span>
              </p>
            </div>
          </div>
          <div class="level-item has-text-centered">
            <div>
              <p class="heading">Storage</p>
              <p class="title has-text-success">
                <span class="icon">
                  <i class="fas fa-check-circle"></i>
                </span>
                <span>Normal</span>
              </p>
            </div>
          </div>
          <div class="level-item has-text-centered">
            <div>
              <p class="heading">Version</p>
              <p class="title">1.0.0</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
  </div>
</section>

<!-- Edit Document Modal -->
<div class="modal" id="editModal">
  <div class="modal-background"></div>
  <div class="modal-card">
    <header class="modal-card-head">
      <p class="modal-card-title">Edit Document</p>
      <button class="delete" aria-label="close"></button>
    </header>
    <section class="modal-card-body">
      <form id="editDocumentForm">
        <div class="field">
          <label class="label">Document Name</label>
          <div class="control">
            <input class="input" type="text" id="editName" name="name" required>
          </div>
        </div>
        <div class="field">
          <label class="label">Author(s)</label>
          <div class="control">
            <input class="input" type="text" id="editAuthors" name="authors" required>
          </div>
        </div>
      </form>
    </section>
    <footer class="modal-card-foot">
      <button class="button is-primary" id="confirmEdit">
        <span class="icon">
          <i class="fas fa-save"></i>
        </span>
        <span>Save Changes</span>
      </button>
      <button class="button" id="cancelEdit">
        <span class="icon">
          <i class="fas fa-times"></i>
        </span>
        <span>Cancel</span>
      </button>
    </footer>
  </div>
</div>

<!-- Delete Document Modal -->
<div class="modal" id="deleteModal">
  <div class="modal-background"></div>
  <div class="modal-card">
    <header class="modal-card-head">
      <p class="modal-card-title">Delete Document</p>
      <button class="delete" aria-label="close"></button>
    </header>
    <section class="modal-card-body">
      <p>Are you sure you want to delete this document? This action cannot be undone.</p>
    </section>
    <footer class="modal-card-foot">
      <button class="button is-danger" id="confirmDelete">
        <span class="icon">
          <i class="fas fa-trash"></i>
        </span>
        <span>Delete</span>
      </button>
      <button class="button" id="cancelDelete">
        <span class="icon">
          <i class="fas fa-times"></i>
        </span>
        <span>Cancel</span>
      </button>
    </footer>
  </div>
</div>

{{#content "scripts"}}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Edit document functionality
    const editModal = document.getElementById('editModal');
    const editForm = document.getElementById('editDocumentForm');
    const editNameInput = document.getElementById('editName');
    const editAuthorsInput = document.getElementById('editAuthors');
    let documentIdToEdit = null;

    // Set up edit buttons
    document.querySelectorAll('.edit-document').forEach(button => {
      button.addEventListener('click', (e) => {
        e.preventDefault();
        documentIdToEdit = button.dataset.id;
        editNameInput.value = button.dataset.name;
        editAuthorsInput.value = button.dataset.authors || '';
        editModal.classList.add('is-active');
      });
    });

    // Confirm edit
    document.getElementById('confirmEdit').addEventListener('click', async () => {
      if (!documentIdToEdit) return;

      try {
        const response = await fetch(`/documents/${documentIdToEdit}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
          },
          body: JSON.stringify({
            name: editNameInput.value,
            authors: editAuthorsInput.value
          })
        });

        if (response.ok) {
          window.location.reload();
        } else {
          const error = await response.json();
          alert(error.message || 'Failed to update document');
        }
      } catch (error) {
        console.error('Error updating document:', error);
        alert('An error occurred while updating the document');
      } finally {
        editModal.classList.remove('is-active');
      }
    });

    // Close edit modal
    document.querySelectorAll('#editModal .modal-background, #editModal .delete, #cancelEdit').forEach(el => {
      el.addEventListener('click', () => {
        editModal.classList.remove('is-active');
      });
    });

    // Delete document functionality
    const deleteModal = document.getElementById('deleteModal');
    let documentIdToDelete = null;

    // Set up delete buttons
    document.querySelectorAll('.delete-document').forEach(button => {
      button.addEventListener('click', (e) => {
        e.preventDefault();
        documentIdToDelete = button.dataset.id;
        deleteModal.classList.add('is-active');
      });
    });

    // Confirm delete
    document.getElementById('confirmDelete').addEventListener('click', async () => {
      if (!documentIdToDelete) return;

      try {
        const response = await fetch(`/documents/${documentIdToDelete}`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
          }
        });

        if (response.ok) {
          window.location.reload();
        } else {
          const error = await response.json();
          alert(error.message || 'Failed to delete document');
        }
      } catch (error) {
        console.error('Error deleting document:', error);
        alert('An error occurred while deleting the document');
      } finally {
        deleteModal.classList.remove('is-active');
      }
    });

    // Close delete modal
    document.querySelectorAll('#deleteModal .modal-background, #deleteModal .delete, #cancelDelete').forEach(el => {
      el.addEventListener('click', () => {
        deleteModal.classList.remove('is-active');
      });
    });
  });
</script>
{{/content}}
