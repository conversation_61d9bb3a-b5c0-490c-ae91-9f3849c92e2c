const moment = require('moment');

module.exports = {
  // Format date helper
  formatDate: function(date, format) {
    return moment(date).format(format || 'YYYY-MM-DD');
  },

  // Equal comparison helper
  eq: function(a, b) {
    return a === b;
  },

  // Not equal comparison helper
  neq: function(a, b) {
    return a !== b;
  },

  // Greater than helper
  gt: function(a, b) {
    return a > b;
  },

  // Greater than or equal helper
  gte: function(a, b) {
    return a >= b;
  },

  // Less than helper
  lt: function(a, b) {
    return a < b;
  },

  // Less than or equal helper
  lte: function(a, b) {
    return a <= b;
  },

  // Logical OR helper
  or: function() {
    for (let i = 0; i < arguments.length - 1; i++) {
      if (arguments[i]) {
        return true;
      }
    }
    return false;
  },

  // Logical AND helper
  and: function() {
    for (let i = 0; i < arguments.length - 1; i++) {
      if (!arguments[i]) {
        return false;
      }
    }
    return true;
  },

  // Split string helper
  split: function(str, separator) {
    if (typeof str !== 'string') return [];
    return str.split(separator);
  },

  // Format file size helper (for documents with size_bytes property)
  formatFileSize: function(document) {
    const bytes = document.size_bytes;
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(2)} KB`;
    if (bytes < 1024 * 1024 * 1024) return `${(bytes / (1024 * 1024)).toFixed(2)} MB`;
    return `${(bytes / (1024 * 1024 * 1024)).toFixed(2)} GB`;
  },

  // Format size in MB to human readable format (for user storage)
  getFormattedSize: function(sizeMB) {
    // Handle undefined, null, or NaN values
    if (sizeMB === undefined || sizeMB === null || isNaN(sizeMB)) return '0 MB';
    const size = Number(sizeMB);
    if (size === 0) return '0 MB';
    if (size < 1024) return `${size} MB`;
    return `${(size / 1024).toFixed(2)} GB`;
  },

  // Calculate storage percentage
  getStoragePercentage: function(usedMB, quotaMB) {
    // Handle undefined, null, or NaN values
    if (usedMB === undefined || usedMB === null || isNaN(usedMB)) usedMB = 0;
    if (quotaMB === undefined || quotaMB === null || isNaN(quotaMB) || quotaMB === 0) return 0;
    const used = Number(usedMB);
    const quota = Number(quotaMB);
    return Math.min(Math.round((used / quota) * 100), 100);
  },

  // File icon helper based on mime type
  fileIcon: function(mimeType) {
    if (mimeType === 'application/pdf') {
      return 'fa-file-pdf';
    } else if (mimeType === 'application/msword' || mimeType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
      return 'fa-file-word';
    } else if (mimeType === 'application/vnd.ms-excel' || mimeType === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
      return 'fa-file-excel';
    } else if (mimeType === 'text/plain') {
      return 'fa-file-alt';
    } else if (mimeType && mimeType.startsWith('image/')) {
      return 'fa-file-image';
    } else {
      return 'fa-file';
    }
  }
};
