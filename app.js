const createError = require('http-errors');
const express = require('express');
const path = require('path');
const cookieParser = require('cookie-parser');
const logger = require('morgan');
const session = require('express-session');
const flash = require('connect-flash');
const methodOverride = require('method-override');
const { passport } = require('./middlewares/auth');

// Import routes
const indexRouter = require('./routes/index');
const authRouter = require('./routes/auth');
const documentsRouter = require('./routes/documents');
const adminRouter = require('./routes/admin');

const app = express();

// View engine setup
app.set('views', path.join(__dirname, 'views'));
app.set('view engine', 'hbs');

// Register partials
const hbs = require('hbs');
const fs = require('fs');

// Register partials directory
hbs.registerPartials(path.join(__dirname, 'views/partials'));

const partials = [
  'document-table-row',
  'edit-document-modal',
  'delete-document-modal',
  'document-modals-script',
  'user-table-row',
  'delete-user-modal',
  'user-modals-script'
];

for (const partial of partials) {
  try {
    const partialContent = fs.readFileSync(path.join(__dirname, `views/partials/${partial}.hbs`), 'utf8');
    hbs.registerPartial(partial, partialContent);
    console.log(`Successfully registered ${partial} partial`);
  } catch (error) {
    console.error(`Error registering ${partial} partial:`, error);
  }
}

// Import and register helpers from helpers/handlebars.js
const handlebarsHelpers = require('./helpers/handlebars');

// Register all helpers from the helpers file
Object.keys(handlebarsHelpers).forEach(helperName => {
  hbs.registerHelper(helperName, handlebarsHelpers[helperName]);
});

// Register additional helpers not in the helpers file
hbs.registerHelper('json', (context) => JSON.stringify(context));

// Math helpers
hbs.registerHelper('math', function(lvalue, operator, rvalue) {
  lvalue = parseFloat(lvalue);
  rvalue = parseFloat(rvalue);

  return {
    "+": lvalue + rvalue,
    "-": lvalue - rvalue,
    "*": lvalue * rvalue,
    "/": lvalue / rvalue,
    "%": lvalue % rvalue
  }[operator];
});

// Alias for neq -> ne (for consistency with template usage)
hbs.registerHelper('ne', handlebarsHelpers.neq);

// Additional helper for formatted file size (used in admin views) - alias to existing helper
hbs.registerHelper('getFormattedSize', handlebarsHelpers.formatFileSize);

// Section helper for content blocks
hbs.registerHelper('section', function(name, options) {
  if (!this._sections) this._sections = {};
  this._sections[name] = options.fn(this);
  return null;
});

// Content block helper - for compatibility with templates using {{#content}} blocks
hbs.registerHelper('content', function(name, options) {
  // If there's a section with this name, render it
  if (this._sections && this._sections[name]) {
    return this._sections[name];
  }
  // Otherwise, render the default content
  return options.fn(this);
});

// Format file size to human readable format (e.g., 1.2 MB, 500 KB)
hbs.registerHelper('formatFileSize', function(bytes) {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
});

// Format date using the format string
hbs.registerHelper('formatDate', function(date, format) {
  if (!date) return '';

  const d = new Date(date);
  if (isNaN(d.getTime())) return '';

  const pad = (n) => n < 10 ? '0' + n : n;

  const formats = {
    'YYYY': d.getFullYear(),
    'MM': pad(d.getMonth() + 1),
    'DD': pad(d.getDate()),
    'HH': pad(d.getHours()),
    'mm': pad(d.getMinutes()),
    'ss': pad(d.getSeconds())
  };

  return format.replace(/(YYYY|MM|DD|HH|mm|ss)/g, (match) => formats[match]);
});

// File icon helper - returns appropriate Font Awesome icon based on file extension
hbs.registerHelper('fileIcon', function(filename) {
  if (!filename) return 'fa-file';

  const extension = filename.split('.').pop().toLowerCase();
  const iconMap = {
    // Documents
    'pdf': 'fa-file-pdf',
    'doc': 'fa-file-word',
    'docx': 'fa-file-word',
    'txt': 'fa-file-alt',
    'rtf': 'fa-file-alt',
    'odt': 'fa-file-word',
    'pages': 'fa-file-word',
    'xls': 'fa-file-excel',
    'xlsx': 'fa-file-excel',
    'csv': 'fa-file-csv',
    'ods': 'fa-file-excel',
    'ppt': 'fa-file-powerpoint',
    'pptx': 'fa-file-powerpoint',
    'odp': 'fa-file-powerpoint',
    'key': 'fa-file-powerpoint',

    // Code
    'js': 'fa-file-code',
    'jsx': 'fa-file-code',
    'ts': 'fa-file-code',
    'html': 'fa-file-code',
    'css': 'fa-file-code',
    'scss': 'fa-file-code',
    'json': 'fa-file-code',
    'xml': 'fa-file-code',
    'py': 'fa-file-code',
    'java': 'fa-file-code',
    'c': 'fa-file-code',
    'cpp': 'fa-file-code',
    'cs': 'fa-file-code',
    'php': 'fa-file-code',
    'rb': 'fa-file-code',
    'go': 'fa-file-code',
    'rs': 'fa-file-code',
    'swift': 'fa-file-code',
    'kt': 'fa-file-code',
    'sh': 'fa-file-code',

    // Images
    'jpg': 'fa-file-image',
    'jpeg': 'fa-file-image',
    'png': 'fa-file-image',
    'gif': 'fa-file-image',
    'bmp': 'fa-file-image',
    'svg': 'fa-file-image',
    'webp': 'fa-file-image',
    'tiff': 'fa-file-image',
    'psd': 'fa-file-image',
    'ai': 'fa-file-image',
    'sketch': 'fa-file-image',

    // Audio
    'mp3': 'fa-file-audio',
    'wav': 'fa-file-audio',
    'ogg': 'fa-file-audio',
    'm4a': 'fa-file-audio',
    'flac': 'fa-file-audio',
    'aac': 'fa-file-audio',
    'wma': 'fa-file-audio',

    // Video
    'mp4': 'fa-file-video',
    'mov': 'fa-file-video',
    'avi': 'fa-file-video',
    'mkv': 'fa-file-video',
    'wmv': 'fa-file-video',
    'flv': 'fa-file-video',
    'webm': 'fa-file-video',
    'm4v': 'fa-file-video',

    // Archives
    'zip': 'fa-file-archive',
    'rar': 'fa-file-archive',
    '7z': 'fa-file-archive',
    'tar': 'fa-file-archive',
    'gz': 'fa-file-archive',
    'bz2': 'fa-file-archive',
    'xz': 'fa-file-archive',
    'pkg': 'fa-file-archive',
    'dmg': 'fa-file-archive',
    'iso': 'fa-file-archive',

    // Data
    'db': 'fa-database',
    'sql': 'fa-database',
    'sqlite': 'fa-database',
    'mdb': 'fa-database',
    'accdb': 'fa-database',
    'csv': 'fa-file-csv',
    'tsv': 'fa-file-csv',
  };

  return iconMap[extension] || 'fa-file';
});

// Middleware
app.use(logger('dev'));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cookieParser());
app.use(express.static(path.join(__dirname, 'public')));
app.use(methodOverride('_method'));

// Session configuration with persistent store
const SequelizeStore = require('connect-session-sequelize')(session.Store);
const { sequelize } = require('./models');

const sessionStore = new SequelizeStore({
  db: sequelize,
  tableName: 'Sessions',
  checkExpirationInterval: 15 * 60 * 1000, // Clean up expired sessions every 15 minutes
  expiration: 24 * 60 * 60 * 1000 // 24 hours
});

app.use(session({
  secret: process.env.SESSION_SECRET || 'your-secret-key',
  store: sessionStore,
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: process.env.NODE_ENV === 'production',
    maxAge: 24 * 60 * 60 * 1000 // 24 hours
  }
}));

// Create the session table if it doesn't exist
sessionStore.sync();

// Passport middleware
app.use(passport.initialize());
app.use(passport.session());

// Flash messages
app.use(flash());

// Global variables
app.use((req, res, next) => {
  res.locals.user = req.user || null;
  res.locals.success_msg = req.flash('success_msg');
  res.locals.error_msg = req.flash('error_msg');
  res.locals.error = req.flash('error');
  next();
});

// Database connection
sequelize.authenticate()
  .then(() => console.log('Database connected...'))
  .catch(err => console.error('Error connecting to the database:', err));

// Routes
app.use('/', indexRouter);
app.use('/auth', authRouter);
app.use('/documents', documentsRouter);
app.use('/admin', adminRouter);

// 404 handler
app.use((req, res, next) => {
  res.status(404).render('error', {
    status: 404,
    message: 'Page not found',
    error: {}
  });
});

// Error handler
app.use((err, req, res, next) => {
  // Set locals, only providing error in development
  res.locals.message = err.message;
  res.locals.error = req.app.get('env') === 'development' ? err : {};

  // Render the error page
  const status = err.status || 500;
  res.status(status);

  if (req.xhr || req.headers.accept.indexOf('json') > -1) {
    // If the request is an API request, return JSON
    res.json({
      success: false,
      message: err.message,
      error: req.app.get('env') === 'development' ? err.stack : {}
    });
  } else {
    // Otherwise, render an error page
    res.render('error', {
      status,
      message: err.message,
      error: req.app.get('env') === 'development' ? err : {}
    });
  }
});

module.exports = app;
