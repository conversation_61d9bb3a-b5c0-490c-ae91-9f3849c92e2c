const express = require('express');
const router = express.Router();
const { check, validationResult } = require('express-validator');
const { isAuthenticated, isAdmin } = require('../middlewares/auth');
const { User, Document } = require('../models');

// GET /admin/dashboard - Admin dashboard
router.get('/dashboard', isAuthenticated, isAdmin, async (req, res, next) => {
  try {
    // Get stats
    const totalUsers = await User.count();
    const totalDocuments = await Document.count();
    const totalStorage = await Document.sum('size_bytes') || 0;
    const totalStorageMB = Math.ceil(totalStorage / (1024 * 1024));

    // Get recent documents
    const recentDocuments = await Document.findAll({
      order: [['created_at', 'DESC']],
      limit: 5,
      include: [{
        model: User,
        as: 'user',
        attributes: ['id', 'email']
      }]
    });

    res.render('admin/dashboard', {
      title: 'Admin Dashboard',
      totalUsers,
      totalDocuments,
      totalStorageMB,
      recentDocuments
    });
  } catch (error) {
    next(error);
  }
});

// GET /admin/users - List all users
router.get('/users', isAuthenticated, isAdmin, async (req, res, next) => {
  try {
    const users = await User.findAll({
      attributes: [
        'id',
        'email',
        'is_admin',
        'quota_mb',
        'created_at',
        [
          Document.sequelize.fn('COUNT', Document.sequelize.col('documents.id')),
          'documents_count'
        ]
      ],
      include: [{
        model: Document,
        as: 'documents',
        attributes: [],
        required: false
      }],
      group: ['User.id'],
      order: [['created_at', 'DESC']]
    });

    // Calculate used storage for each user
    const usersWithStats = await Promise.all(users.map(async (user) => {
      const documents = await Document.findAll({
        where: { user_id: user.id },
        attributes: [[Document.sequelize.fn('SUM', Document.sequelize.col('size_bytes')), 'total_size']],
        raw: true
      });

      const usedBytes = parseInt(documents[0]?.total_size || 0);
      const usedMB = Math.ceil(usedBytes / (1024 * 1024));
      const quotaMB = user.quota_mb;
      const quotaPercentage = Math.min(Math.round((usedMB / quotaMB) * 100), 100);

      return {
        ...user.toJSON(),
        name: user.email.split('@')[0], // Use email prefix as name
        role: user.is_admin ? 'admin' : 'user',
        storage_used: usedMB,
        storage_quota: quotaMB,
        usedMB,
        quotaMB,
        quotaPercentage,
        documents_count: user.documents_count || 0
      };
    }));

    res.render('admin/users', {
      title: 'Manage Users',
      users: usersWithStats,
      currentUser: req.user
    });
  } catch (error) {
    next(error);
  }
});

// GET /admin/users/new - Show create user form
router.get('/users/new', isAuthenticated, isAdmin, (req, res) => {
  res.render('admin/users/new', {
    title: 'Add New User',
    user: { is_admin: false, quota_mb: 100 }
  });
});

// POST /admin/users - Create new user
router.post('/users',
  isAuthenticated,
  isAdmin,
  [
    check('email')
      .isEmail().withMessage('Please enter a valid email')
      .normalizeEmail()
      .custom(async (email) => {
        const user = await User.findOne({ where: { email } });
        if (user) {
          throw new Error('Email is already in use');
        }
        return true;
      }),
    check('password')
      .isLength({ min: 6 }).withMessage('Password must be at least 6 characters long'),
    check('quota_mb')
      .isInt({ min: 1 }).withMessage('Quota must be a positive number')
  ],
  async (req, res, next) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.render('admin/users/new', {
          title: 'Add New User',
          user: req.body,
          error_msg: errors.array()[0].msg
        });
      }

      const { email, password, is_admin, quota_mb } = req.body;

      await User.create({
        email,
        password,
        is_admin: is_admin === 'on',
        quota_mb: parseInt(quota_mb)
      });

      req.flash('success_msg', 'User created successfully');
      res.redirect('/admin/users');
    } catch (error) {
      next(error);
    }
  }
);

// GET /admin/users/:id/edit - Show edit user form
router.get('/users/:id/edit', isAuthenticated, isAdmin, async (req, res, next) => {
  try {
    const user = await User.findByPk(req.params.id, {
      attributes: ['id', 'email', 'is_admin', 'quota_mb']
    });

    if (!user) {
      req.flash('error_msg', 'User not found');
      return res.redirect('/admin/users');
    }

    res.render('admin/users/edit', {
      title: 'Edit User',
      user: user.toJSON()
    });
  } catch (error) {
    next(error);
  }
});

// PUT /admin/users/:id - Update user
router.put('/users/:id',
  isAuthenticated,
  isAdmin,
  [
    check('email')
      .isEmail().withMessage('Please enter a valid email')
      .normalizeEmail()
      .custom(async (email, { req }) => {
        const user = await User.findOne({ where: { email } });
        if (user && user.id !== parseInt(req.params.id)) {
          throw new Error('Email is already in use');
        }
        return true;
      }),
    check('quota_mb')
      .isInt({ min: 1 }).withMessage('Quota must be a positive number')
  ],
  async (req, res, next) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.render('admin/users/edit', {
          title: 'Edit User',
          user: req.body,
          error_msg: errors.array()[0].msg
        });
      }

      const { email, is_admin, quota_mb } = req.body;
      const user = await User.findByPk(req.params.id);

      if (!user) {
        req.flash('error_msg', 'User not found');
        return res.redirect('/admin/users');
      }

      await user.update({
        email,
        is_admin: is_admin === 'on',
        quota_mb: parseInt(quota_mb)
      });

      req.flash('success_msg', 'User updated successfully');
      res.redirect('/admin/users');
    } catch (error) {
      next(error);
    }
  }
);


// DELETE /admin/users/:id - Delete user
router.delete('/users/:id', isAuthenticated, isAdmin, async (req, res, next) => {
  try {
    const user = await User.findByPk(req.params.id);

    if (!user) {
      req.flash('error_msg', 'User not found');
      return res.redirect('/admin/users');
    }

    // Don't allow deleting the last admin
    if (user.is_admin) {
      const adminCount = await User.count({ where: { is_admin: true } });
      if (adminCount <= 1) {
        req.flash('error_msg', 'Cannot delete the last admin user');
        return res.redirect('/admin/users');
      }
    }

    // Delete user's documents
    const documents = await user.getDocuments();
    await Promise.all(documents.map(doc => doc.destroy()));

    // Delete the user
    await user.destroy();

    req.flash('success_msg', 'User deleted successfully');
    res.redirect('/admin/users');
  } catch (error) {
    next(error);
  }
});

// GET /admin/documents - List all documents
router.get('/documents', isAuthenticated, isAdmin, async (req, res, next) => {
  try {
    const documents = await Document.findAll({
      include: [{
        model: User,
        as: 'user',
        attributes: ['id', 'email']
      }],
      order: [['created_at', 'DESC']]
    });

    res.render('admin/documents', {
      title: 'Manage Documents',
      documents
    });
  } catch (error) {
    next(error);
  }
});

// DELETE /admin/documents/:id - Delete document
router.delete('/documents/:id', isAuthenticated, isAdmin, async (req, res, next) => {
  try {
    const document = await Document.findByPk(req.params.id);

    if (!document) {
      return res.status(404).json({
        success: false,
        message: 'Document not found'
      });
    }

    // Delete the file from storage
    const fs = require('fs').promises;
    await fs.unlink(document.file_path).catch(err => {
      console.error(`Error deleting file: ${err.message}`);
    });

    // Delete the document record
    await document.destroy();

    req.flash('success_msg', 'Document deleted successfully');
    return res.redirect('/admin/documents');
  } catch (error) {
    next(error);
  }
});

module.exports = router;
